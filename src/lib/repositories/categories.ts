/**
 * Categories repository for database operations
 */

import { BaseRepository } from './base';
import { Category } from '../database';

export interface CreateCategoryData {
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order?: number;
}

export interface UpdateCategoryData {
  name?: string;
  slug?: string;
  description?: string;
  parent_id?: number;
  sort_order?: number;
}

export class CategoriesRepository extends BaseRepository<Category> {
  constructor() {
    super('categories');
  }

  // Create a new category
  async create(data: CreateCategoryData): Promise<number | null> {
    const categoryData = {
      ...data,
      sort_order: data.sort_order || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return await this.insert(categoryData);
  }

  // Update a category
  async update(id: number, data: UpdateCategoryData): Promise<boolean> {
    return await this.updateById(id, data);
  }

  // Find category by slug
  async findBySlug(slug: string): Promise<Category | null> {
    return await this.findOneWhere('slug = ?', [slug]);
  }

  // Find all root categories (no parent)
  async findRootCategories(): Promise<Category[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE parent_id IS NULL 
      ORDER BY sort_order ASC, name ASC
    `;
    const result = await this.db.query(sql);
    return result.results as Category[];
  }

  // Find child categories of a parent
  async findChildCategories(parentId: number): Promise<Category[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE parent_id = ? 
      ORDER BY sort_order ASC, name ASC
    `;
    const result = await this.db.query(sql, [parentId]);
    return result.results as Category[];
  }

  // Get category hierarchy (parent with children)
  async getCategoryHierarchy(): Promise<(Category & { children?: Category[] })[]> {
    const rootCategories = await this.findRootCategories();
    
    const categoriesWithChildren = await Promise.all(
      rootCategories.map(async (category) => {
        const children = await this.findChildCategories(category.id);
        return {
          ...category,
          children: children.length > 0 ? children : undefined,
        };
      })
    );

    return categoriesWithChildren;
  }

  // Get categories with post counts
  async getCategoriesWithPostCounts(): Promise<(Category & { post_count: number })[]> {
    const sql = `
      SELECT c.*, COUNT(pc.post_id) as post_count
      FROM ${this.tableName} c
      LEFT JOIN post_categories pc ON c.id = pc.category_id
      LEFT JOIN posts p ON pc.post_id = p.id AND p.status = 'published'
      GROUP BY c.id
      ORDER BY c.sort_order ASC, c.name ASC
    `;
    const result = await this.db.query(sql);
    return result.results as (Category & { post_count: number })[];
  }

  // Find categories by name (search)
  async searchByName(query: string, limit = 10): Promise<Category[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE name LIKE ? 
      ORDER BY name ASC 
      LIMIT ?
    `;
    const searchTerm = `%${query}%`;
    const result = await this.db.query(sql, [searchTerm, limit]);
    return result.results as Category[];
  }

  // Get all categories ordered by sort order
  async getAllOrdered(): Promise<Category[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      ORDER BY sort_order ASC, name ASC
    `;
    const result = await this.db.query(sql);
    return result.results as Category[];
  }

  // Check if category has posts
  async hasPosts(categoryId: number): Promise<boolean> {
    const sql = `
      SELECT COUNT(*) as count 
      FROM post_categories pc
      INNER JOIN posts p ON pc.post_id = p.id
      WHERE pc.category_id = ? AND p.status = 'published'
    `;
    const result = await this.db.queryFirst(sql, [categoryId]);
    return (result?.count || 0) > 0;
  }

  // Get category path (for breadcrumbs)
  async getCategoryPath(categoryId: number): Promise<Category[]> {
    const path: Category[] = [];
    let currentId: number | null = categoryId;

    while (currentId) {
      const category = await this.findById(currentId);
      if (!category) break;
      
      path.unshift(category);
      currentId = category.parent_id || null;
    }

    return path;
  }

  // Update sort orders for multiple categories
  async updateSortOrders(updates: { id: number; sort_order: number }[]): Promise<boolean> {
    try {
      for (const update of updates) {
        await this.updateById(update.id, { sort_order: update.sort_order });
      }
      return true;
    } catch (error) {
      console.error('Error updating sort orders:', error);
      return false;
    }
  }
}
