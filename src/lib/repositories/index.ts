/**
 * Repository exports
 */

export { BaseRepository } from './base';
export { PostsRepository } from './posts';
export { CategoriesRepository } from './categories';
export { UsersRepository } from './users';

// Create singleton instances
export const postsRepository = new PostsRepository();
export const categoriesRepository = new CategoriesRepository();
export const usersRepository = new UsersRepository();

// Export types
export type {
  CreatePostData,
  UpdatePostData,
  PostFilters,
} from './posts';

export type {
  CreateCategoryData,
  UpdateCategoryData,
} from './categories';

export type {
  CreateUserData,
  UpdateUserData,
} from './users';
