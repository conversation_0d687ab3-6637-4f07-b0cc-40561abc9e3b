/**
 * Posts repository for database operations
 */

import { BaseRepository } from './base';
import { Post } from '../database';

export interface CreatePostData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  author_id: number;
  status?: 'draft' | 'published' | 'private' | 'trash';
  post_type?: 'post' | 'page';
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  language?: 'en' | 'hi';
  published_at?: string;
}

export interface UpdatePostData {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  status?: 'draft' | 'published' | 'private' | 'trash';
  post_type?: 'post' | 'page';
  featured_image_url?: string;
  meta_title?: string;
  meta_description?: string;
  language?: 'en' | 'hi';
  published_at?: string;
}

export interface PostFilters {
  status?: 'draft' | 'published' | 'private' | 'trash';
  author_id?: number;
  language?: 'en' | 'hi';
  post_type?: 'post' | 'page';
  category_id?: number;
  tag_id?: number;
}

export class PostsRepository extends BaseRepository<Post> {
  constructor() {
    super('posts');
  }

  // Create a new post
  async create(data: CreatePostData): Promise<number | null> {
    const postData = {
      ...data,
      status: data.status || 'draft',
      post_type: data.post_type || 'post',
      language: data.language || 'en',
      view_count: 0,
      comment_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return await this.insert(postData);
  }

  // Update a post
  async update(id: number, data: UpdatePostData): Promise<boolean> {
    return await this.updateById(id, data);
  }

  // Find post by slug
  async findBySlug(slug: string): Promise<Post | null> {
    return await this.findOneWhere('slug = ?', [slug]);
  }

  // Find published posts
  async findPublished(limit = 10, offset = 0): Promise<Post[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE status = 'published' 
      ORDER BY published_at DESC, created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [limit, offset]);
    return result.results as Post[];
  }

  // Find posts by author
  async findByAuthor(authorId: number, limit = 10, offset = 0): Promise<Post[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE author_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [authorId, limit, offset]);
    return result.results as Post[];
  }

  // Find posts by category
  async findByCategory(categoryId: number, limit = 10, offset = 0): Promise<Post[]> {
    const sql = `
      SELECT p.* FROM ${this.tableName} p
      INNER JOIN post_categories pc ON p.id = pc.post_id
      WHERE pc.category_id = ? AND p.status = 'published'
      ORDER BY p.published_at DESC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [categoryId, limit, offset]);
    return result.results as Post[];
  }

  // Find posts by tag
  async findByTag(tagId: number, limit = 10, offset = 0): Promise<Post[]> {
    const sql = `
      SELECT p.* FROM ${this.tableName} p
      INNER JOIN post_tags pt ON p.id = pt.post_id
      WHERE pt.tag_id = ? AND p.status = 'published'
      ORDER BY p.published_at DESC, p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [tagId, limit, offset]);
    return result.results as Post[];
  }

  // Search posts by title or content
  async search(query: string, limit = 10, offset = 0): Promise<Post[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE status = 'published' 
      AND (title LIKE ? OR content LIKE ?)
      ORDER BY published_at DESC, created_at DESC
      LIMIT ? OFFSET ?
    `;
    const searchTerm = `%${query}%`;
    const result = await this.db.query(sql, [searchTerm, searchTerm, limit, offset]);
    return result.results as Post[];
  }

  // Get posts with filters
  async findWithFilters(filters: PostFilters, limit = 10, offset = 0): Promise<Post[]> {
    let whereConditions: string[] = [];
    let params: any[] = [];

    if (filters.status) {
      whereConditions.push('status = ?');
      params.push(filters.status);
    }

    if (filters.author_id) {
      whereConditions.push('author_id = ?');
      params.push(filters.author_id);
    }

    if (filters.language) {
      whereConditions.push('language = ?');
      params.push(filters.language);
    }

    if (filters.post_type) {
      whereConditions.push('post_type = ?');
      params.push(filters.post_type);
    }

    let sql = `SELECT * FROM ${this.tableName}`;
    
    if (whereConditions.length > 0) {
      sql += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const result = await this.db.query(sql, params);
    return result.results as Post[];
  }

  // Increment view count
  async incrementViewCount(id: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET view_count = view_count + 1 WHERE id = ?`;
    const result = await this.db.execute(sql, [id]);
    return result.success;
  }

  // Update comment count
  async updateCommentCount(id: number, count: number): Promise<boolean> {
    const sql = `UPDATE ${this.tableName} SET comment_count = ? WHERE id = ?`;
    const result = await this.db.execute(sql, [count, id]);
    return result.success;
  }

  // Get recent posts
  async getRecent(limit = 5): Promise<Post[]> {
    return await this.findPublished(limit, 0);
  }

  // Get popular posts (by view count)
  async getPopular(limit = 5): Promise<Post[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE status = 'published' 
      ORDER BY view_count DESC, published_at DESC 
      LIMIT ?
    `;
    const result = await this.db.query(sql, [limit]);
    return result.results as Post[];
  }
}
