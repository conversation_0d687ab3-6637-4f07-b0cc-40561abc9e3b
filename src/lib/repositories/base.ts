/**
 * Base repository class with common database operations
 */

import { DatabaseUtils } from '../database';

export abstract class BaseRepository<T> {
  protected db: DatabaseUtils;
  protected tableName: string;

  constructor(tableName: string) {
    this.db = new DatabaseUtils();
    this.tableName = tableName;
  }

  // Find by ID
  async findById(id: number): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const result = await this.db.queryFirst(sql, [id]);
    return result as T | null;
  }

  // Find all records
  async findAll(limit?: number, offset?: number): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName}`;
    const params: any[] = [];

    if (limit) {
      sql += ` LIMIT ?`;
      params.push(limit);
      
      if (offset) {
        sql += ` OFFSET ?`;
        params.push(offset);
      }
    }

    const result = await this.db.query(sql, params);
    return result.results as T[];
  }

  // Count total records
  async count(whereClause?: string, params?: any[]): Promise<number> {
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`;
    }

    const result = await this.db.queryFirst(sql, params || []);
    return result?.count || 0;
  }

  // Delete by ID
  async deleteById(id: number): Promise<boolean> {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.db.execute(sql, [id]);
    return result.success;
  }

  // Find with custom WHERE clause
  async findWhere(whereClause: string, params: any[] = [], limit?: number): Promise<T[]> {
    let sql = `SELECT * FROM ${this.tableName} WHERE ${whereClause}`;
    
    if (limit) {
      sql += ` LIMIT ?`;
      params.push(limit);
    }

    const result = await this.db.query(sql, params);
    return result.results as T[];
  }

  // Find one with custom WHERE clause
  async findOneWhere(whereClause: string, params: any[] = []): Promise<T | null> {
    const sql = `SELECT * FROM ${this.tableName} WHERE ${whereClause} LIMIT 1`;
    const result = await this.db.queryFirst(sql, params);
    return result as T | null;
  }

  // Update by ID
  async updateById(id: number, data: Partial<T>): Promise<boolean> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    
    if (keys.length === 0) {
      return false;
    }

    const setClause = keys.map(key => `${key} = ?`).join(', ');
    const sql = `UPDATE ${this.tableName} SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    
    const result = await this.db.execute(sql, [...values, id]);
    return result.success;
  }

  // Generic insert method
  protected async insert(data: Omit<T, 'id'>): Promise<number | null> {
    const keys = Object.keys(data);
    const values = Object.values(data);
    
    if (keys.length === 0) {
      return null;
    }

    const placeholders = keys.map(() => '?').join(', ');
    const sql = `INSERT INTO ${this.tableName} (${keys.join(', ')}) VALUES (${placeholders})`;
    
    const result = await this.db.execute(sql, values);
    return result.success ? (result.meta?.last_row_id || null) : null;
  }
}
