/**
 * Users repository for database operations
 */

import { BaseRepository } from './base';
import { User } from '../database';

export interface CreateUserData {
  username: string;
  email: string;
  password_hash: string;
  display_name: string;
  bio?: string;
  avatar_url?: string;
  role?: 'subscriber' | 'author' | 'editor' | 'admin';
  status?: 'active' | 'inactive' | 'banned';
  email_verified?: boolean;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  password_hash?: string;
  display_name?: string;
  bio?: string;
  avatar_url?: string;
  role?: 'subscriber' | 'author' | 'editor' | 'admin';
  status?: 'active' | 'inactive' | 'banned';
  email_verified?: boolean;
}

export class UsersRepository extends BaseRepository<User> {
  constructor() {
    super('users');
  }

  // Create a new user
  async create(data: CreateUserData): Promise<number | null> {
    const userData = {
      ...data,
      role: data.role || 'subscriber',
      status: data.status || 'active',
      email_verified: data.email_verified || false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return await this.insert(userData);
  }

  // Update a user
  async update(id: number, data: UpdateUserData): Promise<boolean> {
    return await this.updateById(id, data);
  }

  // Find user by username
  async findByUsername(username: string): Promise<User | null> {
    return await this.findOneWhere('username = ?', [username]);
  }

  // Find user by email
  async findByEmail(email: string): Promise<User | null> {
    return await this.findOneWhere('email = ?', [email]);
  }

  // Find user by username or email (for login)
  async findByUsernameOrEmail(identifier: string): Promise<User | null> {
    return await this.findOneWhere('username = ? OR email = ?', [identifier, identifier]);
  }

  // Find active users
  async findActive(limit = 10, offset = 0): Promise<User[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE status = 'active' 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [limit, offset]);
    return result.results as User[];
  }

  // Find users by role
  async findByRole(role: 'subscriber' | 'author' | 'editor' | 'admin', limit = 10, offset = 0): Promise<User[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE role = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [role, limit, offset]);
    return result.results as User[];
  }

  // Get authors (users who can create posts)
  async getAuthors(): Promise<User[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE role IN ('author', 'editor', 'admin') AND status = 'active'
      ORDER BY display_name ASC
    `;
    const result = await this.db.query(sql);
    return result.results as User[];
  }

  // Search users by display name or username
  async search(query: string, limit = 10): Promise<User[]> {
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE (display_name LIKE ? OR username LIKE ?) AND status = 'active'
      ORDER BY display_name ASC 
      LIMIT ?
    `;
    const searchTerm = `%${query}%`;
    const result = await this.db.query(sql, [searchTerm, searchTerm, limit]);
    return result.results as User[];
  }

  // Check if username exists
  async usernameExists(username: string, excludeId?: number): Promise<boolean> {
    let sql = 'SELECT COUNT(*) as count FROM users WHERE username = ?';
    const params = [username];

    if (excludeId) {
      sql += ' AND id != ?';
      params.push(excludeId);
    }

    const result = await this.db.queryFirst(sql, params);
    return (result?.count || 0) > 0;
  }

  // Check if email exists
  async emailExists(email: string, excludeId?: number): Promise<boolean> {
    let sql = 'SELECT COUNT(*) as count FROM users WHERE email = ?';
    const params = [email];

    if (excludeId) {
      sql += ' AND id != ?';
      params.push(excludeId);
    }

    const result = await this.db.queryFirst(sql, params);
    return (result?.count || 0) > 0;
  }

  // Update user password
  async updatePassword(id: number, passwordHash: string): Promise<boolean> {
    return await this.updateById(id, { password_hash: passwordHash });
  }

  // Verify user email
  async verifyEmail(id: number): Promise<boolean> {
    return await this.updateById(id, { email_verified: true });
  }

  // Update user status
  async updateStatus(id: number, status: 'active' | 'inactive' | 'banned'): Promise<boolean> {
    return await this.updateById(id, { status });
  }

  // Get user statistics
  async getUserStats(userId: number): Promise<{
    post_count: number;
    published_post_count: number;
    comment_count: number;
  }> {
    const postCountSql = 'SELECT COUNT(*) as count FROM posts WHERE author_id = ?';
    const publishedPostCountSql = 'SELECT COUNT(*) as count FROM posts WHERE author_id = ? AND status = "published"';
    const commentCountSql = 'SELECT COUNT(*) as count FROM comments WHERE author_email = (SELECT email FROM users WHERE id = ?)';

    const [postCount, publishedPostCount, commentCount] = await Promise.all([
      this.db.queryFirst(postCountSql, [userId]),
      this.db.queryFirst(publishedPostCountSql, [userId]),
      this.db.queryFirst(commentCountSql, [userId]),
    ]);

    return {
      post_count: postCount?.count || 0,
      published_post_count: publishedPostCount?.count || 0,
      comment_count: commentCount?.count || 0,
    };
  }

  // Get users with post counts
  async getUsersWithPostCounts(limit = 10, offset = 0): Promise<(User & { post_count: number })[]> {
    const sql = `
      SELECT u.*, COUNT(p.id) as post_count
      FROM ${this.tableName} u
      LEFT JOIN posts p ON u.id = p.author_id AND p.status = 'published'
      WHERE u.status = 'active'
      GROUP BY u.id
      ORDER BY post_count DESC, u.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const result = await this.db.query(sql, [limit, offset]);
    return result.results as (User & { post_count: number })[];
  }
}
