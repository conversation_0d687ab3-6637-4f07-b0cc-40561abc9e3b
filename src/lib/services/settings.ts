/**
 * Settings service for managing site configuration
 */

import { DatabaseUtils } from '../database';
import type { Setting } from '../database';

export interface SiteSettings {
  site_title: string;
  site_description: string;
  site_url: string;
  posts_per_page: number;
  comment_moderation: boolean;
  default_language: 'en' | 'hi';
}

export class SettingsService {
  private db: DatabaseUtils;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.db = new DatabaseUtils();
  }

  // Get a setting value
  async get(key: string, defaultValue?: any): Promise<any> {
    // Check cache first
    if (this.isCached(key)) {
      return this.cache.get(key);
    }

    try {
      const sql = 'SELECT value FROM settings WHERE key = ?';
      const result = await this.db.queryFirst(sql, [key]);
      
      let value = result?.value;
      
      if (value === null || value === undefined) {
        value = defaultValue;
      } else {
        // Try to parse JSON values
        try {
          value = JSON.parse(value);
        } catch {
          // If not JSON, return as string
        }
      }

      // Cache the result
      this.setCached(key, value);
      
      return value;
    } catch (error) {
      console.error(`Error getting setting ${key}:`, error);
      return defaultValue;
    }
  }

  // Set a setting value
  async set(key: string, value: any): Promise<boolean> {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      
      const sql = `
        INSERT INTO settings (key, value, updated_at) 
        VALUES (?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(key) DO UPDATE SET 
          value = excluded.value,
          updated_at = CURRENT_TIMESTAMP
      `;
      
      const result = await this.db.execute(sql, [key, stringValue]);
      
      if (result.success) {
        // Update cache
        this.setCached(key, value);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Error setting ${key}:`, error);
      return false;
    }
  }

  // Get multiple settings
  async getMultiple(keys: string[]): Promise<Record<string, any>> {
    const settings: Record<string, any> = {};
    
    // Check cache for each key
    const uncachedKeys: string[] = [];
    for (const key of keys) {
      if (this.isCached(key)) {
        settings[key] = this.cache.get(key);
      } else {
        uncachedKeys.push(key);
      }
    }

    // Fetch uncached keys from database
    if (uncachedKeys.length > 0) {
      try {
        const placeholders = uncachedKeys.map(() => '?').join(',');
        const sql = `SELECT key, value FROM settings WHERE key IN (${placeholders})`;
        const result = await this.db.query(sql, uncachedKeys);
        
        for (const row of result.results) {
          let value = row.value;
          
          // Try to parse JSON values
          try {
            value = JSON.parse(value);
          } catch {
            // If not JSON, keep as string
          }
          
          settings[row.key] = value;
          this.setCached(row.key, value);
        }
      } catch (error) {
        console.error('Error getting multiple settings:', error);
      }
    }

    return settings;
  }

  // Get all site settings
  async getSiteSettings(): Promise<SiteSettings> {
    const keys = [
      'site_title',
      'site_description', 
      'site_url',
      'posts_per_page',
      'comment_moderation',
      'default_language'
    ];
    
    const settings = await this.getMultiple(keys);
    
    return {
      site_title: settings.site_title || 'ZayoTech Blog',
      site_description: settings.site_description || 'Inspirational quotes, shayari, and stories in Hindi and English',
      site_url: settings.site_url || 'https://your-domain.com',
      posts_per_page: parseInt(settings.posts_per_page) || 10,
      comment_moderation: settings.comment_moderation === 'true' || settings.comment_moderation === true,
      default_language: settings.default_language || 'en',
    };
  }

  // Update site settings
  async updateSiteSettings(settings: Partial<SiteSettings>): Promise<boolean> {
    try {
      const updates = Object.entries(settings).map(([key, value]) => 
        this.set(key, value)
      );
      
      const results = await Promise.all(updates);
      return results.every(result => result === true);
    } catch (error) {
      console.error('Error updating site settings:', error);
      return false;
    }
  }

  // Get all settings
  async getAll(): Promise<Setting[]> {
    try {
      const sql = 'SELECT * FROM settings ORDER BY key ASC';
      const result = await this.db.query(sql);
      return result.results as Setting[];
    } catch (error) {
      console.error('Error getting all settings:', error);
      return [];
    }
  }

  // Delete a setting
  async delete(key: string): Promise<boolean> {
    try {
      const sql = 'DELETE FROM settings WHERE key = ?';
      const result = await this.db.execute(sql, [key]);
      
      if (result.success) {
        // Remove from cache
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Error deleting setting ${key}:`, error);
      return false;
    }
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  // Check if a key is cached and not expired
  private isCached(key: string): boolean {
    if (!this.cache.has(key)) {
      return false;
    }
    
    const expiry = this.cacheExpiry.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return false;
    }
    
    return true;
  }

  // Set a value in cache with expiry
  private setCached(key: string, value: any): void {
    this.cache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }
}

// Export singleton instance
export const settingsService = new SettingsService();
