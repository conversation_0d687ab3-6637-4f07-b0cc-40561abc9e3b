/**
 * Blog service for high-level blog operations
 */

import { postsRepository, categoriesRepository, usersRepository } from '../repositories';
import type { Post, Category, User } from '../database';

export interface BlogPost extends Post {
  author: User;
  categories: Category[];
}

export interface BlogListResponse {
  posts: BlogPost[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export class BlogService {
  // Get published posts with author and category information
  async getPublishedPosts(page = 1, limit = 10): Promise<BlogListResponse> {
    const offset = (page - 1) * limit;
    
    // Get posts
    const posts = await postsRepository.findPublished(limit, offset);
    
    // Get total count for pagination
    const total = await postsRepository.count('status = ?', ['published']);
    
    // Enrich posts with author and category data
    const enrichedPosts = await Promise.all(
      posts.map(async (post) => {
        const [author, categories] = await Promise.all([
          this.getPostAuthor(post.id),
          this.getPostCategories(post.id),
        ]);

        return {
          ...post,
          author: author!,
          categories,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return {
      posts: enrichedPosts,
      total,
      page,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  // Get a single post by slug with full details
  async getPostBySlug(slug: string): Promise<BlogPost | null> {
    const post = await postsRepository.findBySlug(slug);
    if (!post || post.status !== 'published') {
      return null;
    }

    // Increment view count
    await postsRepository.incrementViewCount(post.id);

    const [author, categories] = await Promise.all([
      this.getPostAuthor(post.id),
      this.getPostCategories(post.id),
    ]);

    if (!author) {
      return null;
    }

    return {
      ...post,
      author,
      categories,
    };
  }

  // Get posts by category
  async getPostsByCategory(categorySlug: string, page = 1, limit = 10): Promise<BlogListResponse> {
    const category = await categoriesRepository.findBySlug(categorySlug);
    if (!category) {
      return {
        posts: [],
        total: 0,
        page,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      };
    }

    const offset = (page - 1) * limit;
    const posts = await postsRepository.findByCategory(category.id, limit, offset);
    
    // Get total count for this category
    const totalSql = `
      SELECT COUNT(*) as count FROM posts p
      INNER JOIN post_categories pc ON p.id = pc.post_id
      WHERE pc.category_id = ? AND p.status = 'published'
    `;
    const totalResult = await postsRepository['db'].queryFirst(totalSql, [category.id]);
    const total = totalResult?.count || 0;

    // Enrich posts with author and category data
    const enrichedPosts = await Promise.all(
      posts.map(async (post) => {
        const [author, categories] = await Promise.all([
          this.getPostAuthor(post.id),
          this.getPostCategories(post.id),
        ]);

        return {
          ...post,
          author: author!,
          categories,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return {
      posts: enrichedPosts,
      total,
      page,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  // Search posts
  async searchPosts(query: string, page = 1, limit = 10): Promise<BlogListResponse> {
    const offset = (page - 1) * limit;
    const posts = await postsRepository.search(query, limit, offset);
    
    // Get total count for search
    const totalSql = `
      SELECT COUNT(*) as count FROM posts 
      WHERE status = 'published' 
      AND (title LIKE ? OR content LIKE ?)
    `;
    const searchTerm = `%${query}%`;
    const totalResult = await postsRepository['db'].queryFirst(totalSql, [searchTerm, searchTerm]);
    const total = totalResult?.count || 0;

    // Enrich posts with author and category data
    const enrichedPosts = await Promise.all(
      posts.map(async (post) => {
        const [author, categories] = await Promise.all([
          this.getPostAuthor(post.id),
          this.getPostCategories(post.id),
        ]);

        return {
          ...post,
          author: author!,
          categories,
        };
      })
    );

    const totalPages = Math.ceil(total / limit);

    return {
      posts: enrichedPosts,
      total,
      page,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  // Get recent posts
  async getRecentPosts(limit = 5): Promise<BlogPost[]> {
    const posts = await postsRepository.getRecent(limit);
    
    return await Promise.all(
      posts.map(async (post) => {
        const [author, categories] = await Promise.all([
          this.getPostAuthor(post.id),
          this.getPostCategories(post.id),
        ]);

        return {
          ...post,
          author: author!,
          categories,
        };
      })
    );
  }

  // Get popular posts
  async getPopularPosts(limit = 5): Promise<BlogPost[]> {
    const posts = await postsRepository.getPopular(limit);
    
    return await Promise.all(
      posts.map(async (post) => {
        const [author, categories] = await Promise.all([
          this.getPostAuthor(post.id),
          this.getPostCategories(post.id),
        ]);

        return {
          ...post,
          author: author!,
          categories,
        };
      })
    );
  }

  // Get all categories with post counts
  async getCategories(): Promise<(Category & { post_count: number })[]> {
    return await categoriesRepository.getCategoriesWithPostCounts();
  }

  // Get category hierarchy
  async getCategoryHierarchy(): Promise<(Category & { children?: Category[] })[]> {
    return await categoriesRepository.getCategoryHierarchy();
  }

  // Helper method to get post author
  private async getPostAuthor(postId: number): Promise<User | null> {
    const post = await postsRepository.findById(postId);
    if (!post) return null;
    
    return await usersRepository.findById(post.author_id);
  }

  // Helper method to get post categories
  private async getPostCategories(postId: number): Promise<Category[]> {
    const sql = `
      SELECT c.* FROM categories c
      INNER JOIN post_categories pc ON c.id = pc.category_id
      WHERE pc.post_id = ?
      ORDER BY c.name ASC
    `;
    const result = await categoriesRepository['db'].query(sql, [postId]);
    return result.results as Category[];
  }
}

// Export singleton instance
export const blogService = new BlogService();
