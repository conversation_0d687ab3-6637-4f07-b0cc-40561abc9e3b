import { <PERSON><PERSON>Header } from '@/components/layout/StaticHeader';
import { <PERSON><PERSON>Footer } from '@/components/layout/StaticFooter';
import { StaticPostCard } from '@/components/blog/StaticPostCard';

// Mock data for demonstration - in a real app, this would come from your API/database
const mockPosts = [
  {
    id: 1,
    title: "Getting Started with Next.js 15: A Complete Guide",
    slug: "getting-started-nextjs-15-complete-guide",
    excerpt: "Learn how to build modern web applications with Next.js 15, including the latest features like Server Components, App Router, and improved performance optimizations.",
    featured_image_url: "/blog-images/web-architecture.jpg",
    published_at: "2025-01-25T10:00:00Z",
    view_count: 1250,
    comment_count: 23,
    read_time: 8,
    author: {
      display_name: "<PERSON>eloper",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "Web Development", slug: "web-development" }
    ],
    language: "en" as const
  },
  {
    id: 2,
    title: "Understanding React Server Components",
    slug: "understanding-react-server-components",
    excerpt: "Dive deep into React Server Components and learn how they can improve your application's performance and user experience.",
    featured_image_url: "/blog-images/javascript-techniques.jpg",
    published_at: "2025-01-24T14:30:00Z",
    view_count: 890,
    comment_count: 15,
    read_time: 6,
    author: {
      display_name: "Sarah React",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "Programming", slug: "programming" }
    ],
    language: "en" as const
  },
  {
    id: 3,
    title: "AI-Powered Development Tools in 2025",
    slug: "ai-powered-development-tools-2025",
    excerpt: "Explore the latest AI tools that are revolutionizing software development, from code generation to automated testing and deployment.",
    featured_image_url: "/blog-images/html-fundamentals.jpg",
    published_at: "2025-01-23T09:15:00Z",
    view_count: 2100,
    comment_count: 42,
    read_time: 10,
    author: {
      display_name: "Alex AI",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "AI", slug: "ai" }
    ],
    language: "en" as const
  },
  {
    id: 4,
    title: "Building Responsive Layouts with CSS Grid",
    slug: "building-responsive-layouts-css-grid",
    excerpt: "Master CSS Grid to create complex, responsive layouts that work perfectly across all devices and screen sizes.",
    featured_image_url: "/blog-images/css-animations.jpg",
    published_at: "2025-01-22T16:45:00Z",
    view_count: 675,
    comment_count: 18,
    read_time: 7,
    author: {
      display_name: "Emma CSS",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "Tutorials", slug: "tutorials" }
    ],
    language: "en" as const
  },
  {
    id: 5,
    title: "TypeScript Best Practices for Large Applications",
    slug: "typescript-best-practices-large-applications",
    excerpt: "Learn essential TypeScript patterns and practices for building maintainable, scalable applications in enterprise environments.",
    featured_image_url: "/blog-images/html-images.jpg",
    published_at: "2025-01-21T11:20:00Z",
    view_count: 1450,
    comment_count: 31,
    read_time: 12,
    author: {
      display_name: "Mike TypeScript",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "Programming", slug: "programming" }
    ],
    language: "en" as const
  },
  {
    id: 6,
    title: "Modern Database Design Patterns",
    slug: "modern-database-design-patterns",
    excerpt: "Explore contemporary database design patterns including microservices data management, event sourcing, and CQRS implementations.",
    featured_image_url: "/blog-images/web-navigation.jpg",
    published_at: "2025-01-20T13:00:00Z",
    view_count: 820,
    comment_count: 25,
    read_time: 9,
    author: {
      display_name: "David Database",
      avatar_url: "/profile-image.jpg"
    },
    categories: [
      { name: "Technology", slug: "technology" }
    ],
    language: "en" as const
  }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50">
      <StaticHeader />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="text-center py-12 mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Technology & Tutorials
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the latest in web development, programming, AI, and technology through our comprehensive tutorials and guides.
          </p>
        </section>

        {/* Blog Posts Grid */}
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {mockPosts.map((post) => (
            <StaticPostCard key={post.id} post={post} />
          ))}
        </section>
      </main>

      <StaticFooter />
    </div>
  );
}
