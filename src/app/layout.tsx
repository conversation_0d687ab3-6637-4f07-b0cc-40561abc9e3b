import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Teknorial Blog - Technology & Tutorials",
  description: "A modern blog about technology, programming, and tutorials. Inspired by clean design and great user experience.",
  keywords: "technology, programming, tutorials, blog, web development, AI, React, Next.js",
  authors: [{ name: "Blog Author" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "Teknorial Blog - Technology & Tutorials",
    description: "A modern blog about technology, programming, and tutorials.",
    type: "website",
    locale: "en_US",
    siteName: "Teknorial Blog",
  },
  twitter: {
    card: "summary_large_image",
    title: "Teknorial Blog - Technology & Tutorials",
    description: "A modern blog about technology, programming, and tutorials.",
    creator: "@teknorial",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body className="antialiased">
        <div id="wrapper">
          {children}
        </div>
      </body>
    </html>
  );
}
