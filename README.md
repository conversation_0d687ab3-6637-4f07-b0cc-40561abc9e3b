# Teknorial Blog Frontend

A modern blog frontend inspired by the clean design of [Teknorial.com](https://teknorial.com/). Built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

- **Modern Design**: Clean, responsive layout inspired by Teknorial.com
- **Blog Post Cards**: Beautiful card-based layout with featured images, categories, and author information
- **Responsive Grid**: Masonry-style grid layout that adapts to different screen sizes
- **Search Functionality**: Modal-based search overlay with smooth animations
- **Performance Optimized**: Image optimization, lazy loading, and efficient rendering
- **Accessibility**: Proper ARIA labels, focus management, and keyboard navigation
- **SEO Ready**: Comprehensive meta tags and structured data

## Design Elements

### Header
- Logo with tagline
- Responsive navigation menu
- Search functionality with modal overlay
- Mobile-friendly hamburger menu

### Blog Cards
- Featured images with hover effects
- Category tags with color coding
- Author information with avatars
- Read time estimation
- Smooth hover animations

### Footer
- Social media links
- Clean copyright section
- Responsive design

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom utilities
- **Fonts**: <PERSON>eist Sans and Geist Mono
- **Images**: Next.js Image component with optimization
- **Icons**: Custom SVG icons and Lucide React

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and utilities
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Homepage with blog grid
├── components/
│   ├── blog/
│   │   └── StaticPostCard.tsx    # Blog post card component
│   └── layout/
│       ├── StaticHeader.tsx      # Header with navigation
│       └── StaticFooter.tsx      # Footer with social links
└── types/                   # TypeScript type definitions
```

## Customization

### Colors
The design uses a blue-based color scheme. You can customize colors in `globals.css`:

```css
:root {
  --primary: #007acc;
  --primary-hover: #005a99;
  /* ... other color variables */
}
```

### Blog Posts
Mock data is currently used for demonstration. Replace the `mockPosts` array in `page.tsx` with your actual blog data or API calls.

### Images
Place blog images in the `public/blog-images/` directory and reference them in your post data.

## Performance Features

- **Image Optimization**: Automatic WebP conversion and responsive sizing
- **Lazy Loading**: Images load only when needed
- **CSS Optimization**: Critical CSS inlining and efficient styling
- **Font Optimization**: Automatic font loading and display optimization

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all interactive elements
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: WCAG compliant color combinations

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is open source and available under the [MIT License](LICENSE).
