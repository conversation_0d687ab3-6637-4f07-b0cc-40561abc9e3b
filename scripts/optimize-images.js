const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function optimizeImages() {
  console.log('🖼️ Starting image optimization...');
  
  const inputDir = 'public/images';
  const outputDir = 'public/optimized';

  // Create directories if they don't exist
  if (!fs.existsSync(inputDir)) {
    fs.mkdirSync(inputDir, { recursive: true });
    console.log('📁 Created public/images directory');
    
    // Create a sample image for testing
    await sharp({
      create: {
        width: 1200,
        height: 800,
        channels: 3,
        background: { r: 100, g: 150, b: 200 }
      }
    })
    .png()
    .toFile(path.join(inputDir, 'sample-hero.png'));
    
    console.log('✅ Created sample image for testing');
  }

  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const images = fs.readdirSync(inputDir)
    .filter(file => /\.(jpg|jpeg|png|webp)$/i.test(file));

  if (images.length === 0) {
    console.log('ℹ️ No images found to optimize');
    return;
  }

  for (const image of images) {
    const inputPath = path.join(inputDir, image);
    const name = path.parse(image).name;
    const ext = path.parse(image).ext.toLowerCase();

    console.log(`🔄 Processing ${image}...`);

    try {
      // Generate WebP versions
      await sharp(inputPath)
        .webp({ quality: 85, effort: 6 })
        .toFile(path.join(outputDir, `${name}.webp`));

      // Generate AVIF versions (more efficient but newer format)
      await sharp(inputPath)
        .avif({ quality: 80, effort: 6 })
        .toFile(path.join(outputDir, `${name}.avif`));

      // Generate responsive sizes for WebP
      const sizes = [400, 800, 1200, 1600];
      for (const size of sizes) {
        await sharp(inputPath)
          .resize(size, null, { 
            withoutEnlargement: true,
            fit: 'inside'
          })
          .webp({ quality: 85, effort: 6 })
          .toFile(path.join(outputDir, `${name}-${size}w.webp`));
      }

      // Generate optimized original format
      if (ext === '.jpg' || ext === '.jpeg') {
        await sharp(inputPath)
          .jpeg({ quality: 85, progressive: true })
          .toFile(path.join(outputDir, `${name}-optimized${ext}`));
      } else if (ext === '.png') {
        await sharp(inputPath)
          .png({ quality: 85, progressive: true })
          .toFile(path.join(outputDir, `${name}-optimized${ext}`));
      }

    } catch (error) {
      console.error(`❌ Error processing ${image}:`, error.message);
    }
  }

  console.log(`✅ Optimized ${images.length} images`);
  console.log(`📁 Output directory: ${outputDir}`);
}

// Run if called directly
if (require.main === module) {
  optimizeImages().catch(console.error);
}

module.exports = { optimizeImages };
