# Authentication & Authorization System Design

## Overview
This document outlines the comprehensive authentication and authorization system for the blog website, including user roles, permissions, security measures, and implementation strategies.

## User Roles and Permissions

### 1. Role Hierarchy
```
Admin (Full Access)
├── Editor (Content Management)
│   ├── Author (Own Content)
│   │   └── Subscriber (Read Only)
```

### 2. Role Definitions

#### Subscriber
- **Purpose**: Regular users who can comment and interact
- **Permissions**:
  - Read published posts and pages
  - Submit comments (subject to moderation)
  - Manage own profile
  - Subscribe to newsletters

#### Author
- **Purpose**: Content creators who can write and manage their own posts
- **Permissions**:
  - All Subscriber permissions
  - Create, edit, and delete own posts
  - Upload media files
  - Manage own post categories and tags
  - View own post analytics
  - Moderate comments on own posts

#### Editor
- **Purpose**: Content managers who oversee content quality and publication
- **Permissions**:
  - All Author permissions
  - Edit and delete any posts
  - Manage all categories and tags
  - Moderate all comments
  - Manage media library
  - View site analytics
  - Manage other Authors' content

#### Admin
- **Purpose**: Site administrators with full system access
- **Permissions**:
  - All Editor permissions
  - Manage users and roles
  - Configure site settings
  - Access system logs
  - Manage plugins and themes
  - Database management
  - Security settings

## Authentication System

### 1. JWT-Based Authentication

#### Token Structure
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": 123,
    "username": "johndoe",
    "email": "<EMAIL>",
    "role": "author",
    "permissions": ["read:posts", "write:own_posts", "upload:media"],
    "iat": 1642694400,
    "exp": 1642698000,
    "iss": "blog-api",
    "aud": "blog-frontend"
  }
}
```

#### Token Management
```typescript
// lib/auth.ts
interface TokenPayload {
  user_id: number;
  username: string;
  email: string;
  role: UserRole;
  permissions: string[];
  iat: number;
  exp: number;
}

class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET!;
  private readonly ACCESS_TOKEN_EXPIRY = '15m';
  private readonly REFRESH_TOKEN_EXPIRY = '7d';

  generateAccessToken(user: User): string {
    return jwt.sign(
      {
        user_id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: this.getRolePermissions(user.role),
      },
      this.JWT_SECRET,
      {
        expiresIn: this.ACCESS_TOKEN_EXPIRY,
        issuer: 'blog-api',
        audience: 'blog-frontend'
      }
    );
  }

  generateRefreshToken(userId: number): string {
    return jwt.sign(
      { user_id: userId, type: 'refresh' },
      this.JWT_SECRET,
      { expiresIn: this.REFRESH_TOKEN_EXPIRY }
    );
  }

  verifyToken(token: string): TokenPayload {
    return jwt.verify(token, this.JWT_SECRET) as TokenPayload;
  }

  private getRolePermissions(role: UserRole): string[] {
    const permissions = {
      subscriber: [
        'read:posts',
        'read:comments',
        'create:comments',
        'update:own_profile'
      ],
      author: [
        ...this.getRolePermissions('subscriber'),
        'create:posts',
        'update:own_posts',
        'delete:own_posts',
        'upload:media',
        'moderate:own_comments'
      ],
      editor: [
        ...this.getRolePermissions('author'),
        'update:any_posts',
        'delete:any_posts',
        'manage:categories',
        'manage:tags',
        'moderate:all_comments',
        'view:analytics'
      ],
      admin: [
        ...this.getRolePermissions('editor'),
        'manage:users',
        'manage:settings',
        'view:logs',
        'system:admin'
      ]
    };

    return permissions[role] || permissions.subscriber;
  }
}
```

### 2. Session Management

#### Database Schema
```sql
-- User sessions table (already defined in main schema)
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token_hash TEXT NOT NULL,
    device_info TEXT,
    ip_address TEXT,
    user_agent TEXT,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);
```

#### Session Service
```typescript
// lib/sessionService.ts
class SessionService {
  async createSession(userId: number, deviceInfo: SessionDeviceInfo): Promise<Session> {
    const sessionId = generateUUID();
    const refreshToken = generateRefreshToken(userId);
    const refreshTokenHash = await bcrypt.hash(refreshToken, 10);

    const session = await db.insert(userSessions).values({
      id: sessionId,
      user_id: userId,
      refresh_token_hash: refreshTokenHash,
      device_info: JSON.stringify(deviceInfo),
      ip_address: deviceInfo.ip,
      user_agent: deviceInfo.userAgent,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });

    return { sessionId, refreshToken };
  }

  async validateSession(sessionId: string, refreshToken: string): Promise<User | null> {
    const session = await db.select()
      .from(userSessions)
      .where(eq(userSessions.id, sessionId))
      .limit(1);

    if (!session.length || session[0].expires_at < new Date()) {
      return null;
    }

    const isValidToken = await bcrypt.compare(refreshToken, session[0].refresh_token_hash);
    if (!isValidToken) {
      return null;
    }

    // Update last activity
    await db.update(userSessions)
      .set({ last_activity: new Date() })
      .where(eq(userSessions.id, sessionId));

    return await this.getUserById(session[0].user_id);
  }

  async revokeSession(sessionId: string): Promise<void> {
    await db.delete(userSessions)
      .where(eq(userSessions.id, sessionId));
  }

  async revokeAllUserSessions(userId: number): Promise<void> {
    await db.delete(userSessions)
      .where(eq(userSessions.user_id, userId));
  }
}
```

## Authorization System

### 1. Permission-Based Access Control

#### Permission Definitions
```typescript
// types/permissions.ts
export const PERMISSIONS = {
  // Post permissions
  'read:posts': 'Read published posts',
  'create:posts': 'Create new posts',
  'update:own_posts': 'Update own posts',
  'update:any_posts': 'Update any posts',
  'delete:own_posts': 'Delete own posts',
  'delete:any_posts': 'Delete any posts',

  // Comment permissions
  'read:comments': 'Read comments',
  'create:comments': 'Create comments',
  'moderate:own_comments': 'Moderate comments on own posts',
  'moderate:all_comments': 'Moderate all comments',

  // Media permissions
  'upload:media': 'Upload media files',
  'manage:media': 'Manage all media files',

  // Category and tag permissions
  'manage:categories': 'Manage categories',
  'manage:tags': 'Manage tags',

  // User permissions
  'update:own_profile': 'Update own profile',
  'manage:users': 'Manage all users',

  // System permissions
  'view:analytics': 'View analytics',
  'manage:settings': 'Manage site settings',
  'view:logs': 'View system logs',
  'system:admin': 'Full system administration'
} as const;

export type Permission = keyof typeof PERMISSIONS;
```

#### Authorization Middleware
```typescript
// middleware/auth.ts
export function requireAuth(req: Request, res: Response, next: NextFunction) {
  const token = extractTokenFromHeader(req.headers.authorization);

  if (!token) {
    return res.status(401).json({
      success: false,
      error: { code: 'AUTHENTICATION_REQUIRED', message: 'Authentication required' }
    });
  }

  try {
    const payload = authService.verifyToken(token);
    req.user = payload;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: { code: 'INVALID_TOKEN', message: 'Invalid or expired token' }
    });
  }
}

export function requirePermission(permission: Permission) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: { code: 'AUTHENTICATION_REQUIRED', message: 'Authentication required' }
      });
    }

    if (!req.user.permissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        error: { code: 'AUTHORIZATION_FAILED', message: 'Insufficient permissions' }
      });
    }

    next();
  };
}

export function requireRole(role: UserRole) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: { code: 'AUTHENTICATION_REQUIRED', message: 'Authentication required' }
      });
    }

    const roleHierarchy = { subscriber: 0, author: 1, editor: 2, admin: 3 };
    const userLevel = roleHierarchy[req.user.role];
    const requiredLevel = roleHierarchy[role];

    if (userLevel < requiredLevel) {
      return res.status(403).json({
        success: false,
        error: { code: 'AUTHORIZATION_FAILED', message: 'Insufficient role level' }
      });
    }

    next();
  };
}
```

### 2. Resource-Based Authorization

#### Ownership Validation
```typescript
// middleware/ownership.ts
export function requireOwnership(resourceType: 'post' | 'comment' | 'media') {
  return async (req: Request, res: Response, next: NextFunction) => {
    const resourceId = parseInt(req.params.id);
    const userId = req.user!.user_id;

    try {
      let isOwner = false;

      switch (resourceType) {
        case 'post':
          const post = await db.select()
            .from(posts)
            .where(eq(posts.id, resourceId))
            .limit(1);
          isOwner = post.length > 0 && post[0].author_id === userId;
          break;

        case 'comment':
          const comment = await db.select()
            .from(comments)
            .where(eq(comments.id, resourceId))
            .limit(1);
          // For comments, check if user owns the comment or the post
          if (comment.length > 0) {
            const post = await db.select()
              .from(posts)
              .where(eq(posts.id, comment[0].post_id))
              .limit(1);
            isOwner = post.length > 0 && post[0].author_id === userId;
          }
          break;

        case 'media':
          const media = await db.select()
            .from(mediaTable)
            .where(eq(mediaTable.id, resourceId))
            .limit(1);
          isOwner = media.length > 0 && media[0].uploaded_by === userId;
          break;
      }

      if (!isOwner && !req.user!.permissions.includes(`update:any_${resourceType}s`)) {
        return res.status(403).json({
          success: false,
          error: { code: 'AUTHORIZATION_FAILED', message: 'You can only modify your own resources' }
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: { code: 'SERVER_ERROR', message: 'Failed to validate ownership' }
      });
    }
  };
}
```

## Security Measures

### 1. Password Security

#### Password Hashing
```typescript
// lib/password.ts
import bcrypt from 'bcrypt';
import zxcvbn from 'zxcvbn';

class PasswordService {
  private readonly SALT_ROUNDS = 12;
  private readonly MIN_STRENGTH = 2; // 0-4 scale

  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const result = zxcvbn(password);

    return {
      isValid: result.score >= this.MIN_STRENGTH,
      score: result.score,
      feedback: result.feedback.suggestions
    };
  }

  generateSecurePassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    return password;
  }
}
```

### 2. Rate Limiting

#### Implementation
```typescript
// middleware/rateLimit.ts
import { RateLimiterRedis } from 'rate-limiter-flexible';

const rateLimiters = {
  login: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'login_fail',
    points: 5, // Number of attempts
    duration: 900, // Per 15 minutes
    blockDuration: 900, // Block for 15 minutes
  }),

  register: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'register',
    points: 3, // Number of registrations
    duration: 3600, // Per hour
    blockDuration: 3600, // Block for 1 hour
  }),

  api: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'api',
    points: 1000, // Number of requests
    duration: 60, // Per minute
    blockDuration: 60, // Block for 1 minute
  }),

  upload: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'upload',
    points: 10, // Number of uploads
    duration: 60, // Per minute
    blockDuration: 300, // Block for 5 minutes
  })
};

export function createRateLimit(type: keyof typeof rateLimiters) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const key = req.ip || 'unknown';

    try {
      await rateLimiters[type].consume(key);
      next();
    } catch (rejRes) {
      const remainingPoints = rejRes.remainingPoints || 0;
      const msBeforeNext = rejRes.msBeforeNext || 0;

      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': rateLimiters[type].points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      return res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later'
        }
      });
    }
  };
}
```

### 3. Input Validation and Sanitization

#### Validation Schemas
```typescript
// lib/validation.ts
import { z } from 'zod';

export const schemas = {
  register: z.object({
    username: z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(30, 'Username must be less than 30 characters')
      .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),

    email: z.string()
      .email('Invalid email address')
      .max(255, 'Email must be less than 255 characters'),

    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .max(128, 'Password must be less than 128 characters'),

    display_name: z.string()
      .min(1, 'Display name is required')
      .max(100, 'Display name must be less than 100 characters')
  }),

  login: z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(1, 'Password is required')
  }),

  createPost: z.object({
    title: z.string()
      .min(1, 'Title is required')
      .max(255, 'Title must be less than 255 characters'),

    content: z.string()
      .min(1, 'Content is required')
      .max(50000, 'Content must be less than 50,000 characters'),

    excerpt: z.string()
      .max(500, 'Excerpt must be less than 500 characters')
      .optional(),

    status: z.enum(['draft', 'published', 'private']),

    categories: z.array(z.number().positive()).optional(),

    tags: z.array(z.string().max(50)).optional(),

    language: z.enum(['en', 'hi']).default('en')
  }),

  updateProfile: z.object({
    display_name: z.string()
      .min(1, 'Display name is required')
      .max(100, 'Display name must be less than 100 characters'),

    bio: z.string()
      .max(500, 'Bio must be less than 500 characters')
      .optional(),

    avatar_url: z.string().url('Invalid avatar URL').optional()
  })
};

export function validateRequest(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validated = schema.parse(req.body);
      req.body = validated;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          }
        });
      }
      next(error);
    }
  };
}
```

### 4. CSRF Protection

#### Implementation
```typescript
// middleware/csrf.ts
import csrf from 'csurf';

export const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  }
});

export function getCsrfToken(req: Request, res: Response) {
  res.json({
    success: true,
    data: { csrfToken: req.csrfToken() }
  });
}
```

## Frontend Authentication

### 1. Auth Context
```typescript
// contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (data: RegisterData) => Promise<void>;
  hasPermission: (permission: Permission) => boolean;
  hasRole: (role: UserRole) => boolean;
}

export const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      // Verify token and get user info
      verifyToken(token).then(setUser).catch(() => {
        localStorage.removeItem('token');
      }).finally(() => {
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    const { user, token } = response.data;

    localStorage.setItem('token', token);
    setUser(user);
  };

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
    // Optionally call logout endpoint to invalidate server session
    api.post('/auth/logout').catch(() => {});
  };

  const hasPermission = (permission: Permission): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const hasRole = (role: UserRole): boolean => {
    if (!user) return false;
    const roleHierarchy = { subscriber: 0, author: 1, editor: 2, admin: 3 };
    return roleHierarchy[user.role] >= roleHierarchy[role];
  };

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated: !!user,
      isLoading,
      login,
      logout,
      register,
      hasPermission,
      hasRole
    }}>
      {children}
    </AuthContext.Provider>
  );
}
```

### 2. Protected Routes
```typescript
// components/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  permission?: Permission;
  role?: UserRole;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  permission,
  role,
  fallback = <div>Access denied</div>
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading, hasPermission, hasRole } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (permission && !hasPermission(permission)) {
    return fallback;
  }

  if (role && !hasRole(role)) {
    return fallback;
  }

  return <>{children}</>;
}

// Usage examples
export function AdminDashboard() {
  return (
    <ProtectedRoute role="admin">
      <AdminDashboardContent />
    </ProtectedRoute>
  );
}

export function PostEditor() {
  return (
    <ProtectedRoute permission="create:posts">
      <PostEditorContent />
    </ProtectedRoute>
  );
}
```

### 3. Auth Forms
```typescript
// components/auth/LoginForm.tsx
export function LoginForm() {
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(schemas.login),
    defaultValues: {
      email: '',
      password: ''
    }
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      await login(data.email, data.password);
      toast.success('Login successful');
      // Redirect will happen automatically via AuthContext
    } catch (error) {
      toast.error('Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Sign In</CardTitle>
        <CardDescription>
          Enter your email and password to access your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Link
          href="/forgot-password"
          className="text-sm text-muted-foreground hover:underline"
        >
          Forgot your password?
        </Link>
        <div className="text-sm text-muted-foreground">
          Don't have an account?{' '}
          <Link href="/register" className="text-primary hover:underline">
            Sign up
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
```

## Security Best Practices

### 1. Environment Configuration
```bash
# .env.production
JWT_SECRET=your-super-secure-jwt-secret-key-here
REFRESH_TOKEN_SECRET=your-refresh-token-secret-key
SESSION_SECRET=your-session-secret-key

# Database
DATABASE_URL=your-secure-database-connection-string

# Redis (for rate limiting and caching)
REDIS_URL=your-redis-connection-string

# Email service (for password reset)
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password

# Security headers
CORS_ORIGIN=https://yourdomain.com
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
```

### 2. Security Headers
```typescript
// middleware/security.ts
import helmet from 'helmet';

export const securityMiddleware = [
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https://media.yourdomain.com"],
        scriptSrc: ["'self'"],
        connectSrc: ["'self'", "https://api.yourdomain.com"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }),

  // CORS configuration
  cors({
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
  })
];
```

### 3. Audit Logging
```typescript
// lib/auditLog.ts
interface AuditLogEntry {
  user_id?: number;
  action: string;
  resource_type: string;
  resource_id?: number;
  ip_address: string;
  user_agent: string;
  details?: Record<string, any>;
  timestamp: Date;
}

class AuditLogger {
  async log(entry: AuditLogEntry): Promise<void> {
    // Log to database
    await db.insert(auditLogs).values(entry);

    // Also log to external service for security monitoring
    if (this.isSecuritySensitive(entry.action)) {
      await this.logToSecurityService(entry);
    }
  }

  private isSecuritySensitive(action: string): boolean {
    const sensitiveActions = [
      'user.login.failed',
      'user.password.changed',
      'user.role.changed',
      'admin.settings.changed',
      'user.account.locked'
    ];

    return sensitiveActions.includes(action);
  }

  private async logToSecurityService(entry: AuditLogEntry): Promise<void> {
    // Send to external security monitoring service
    // Implementation depends on your chosen service
  }
}

export const auditLogger = new AuditLogger();

// Usage in middleware
export function auditMiddleware(action: string, resourceType: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    res.on('finish', () => {
      auditLogger.log({
        user_id: req.user?.user_id,
        action,
        resource_type: resourceType,
        resource_id: parseInt(req.params.id) || undefined,
        ip_address: req.ip || 'unknown',
        user_agent: req.get('User-Agent') || 'unknown',
        details: {
          method: req.method,
          url: req.url,
          status_code: res.statusCode
        },
        timestamp: new Date()
      });
    });

    next();
  };
}
```

This comprehensive authentication and authorization system provides robust security while maintaining usability and scalability for your blog website.
```
```