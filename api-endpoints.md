# API Endpoints Architecture for Blog Website

## Overview
This document defines the RESTful API endpoints for the blog website, including authentication, content management, and media handling.

## Base URL Structure
```
Production: https://api.yourblog.com/v1
Development: https://dev-api.yourblog.com/v1
```

## Authentication Endpoints

### POST /auth/register
Register a new user account
```json
Request:
{
  "username": "string",
  "email": "string",
  "password": "string",
  "display_name": "string"
}

Response:
{
  "success": true,
  "user": {
    "id": 1,
    "username": "johndo<PERSON>",
    "email": "<EMAIL>",
    "display_name": "<PERSON>",
    "role": "subscriber"
  },
  "token": "jwt_token_here"
}
```

### POST /auth/login
Authenticate user and get access token
```json
Request:
{
  "email": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "user": { /* user object */ },
  "token": "jwt_token_here",
  "expires_in": 3600
}
```

### POST /auth/logout
Invalidate current session
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "Logged out successfully"
}
```

### POST /auth/refresh
Refresh access token
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "token": "new_jwt_token",
  "expires_in": 3600
}
```

### POST /auth/forgot-password
Request password reset
```json
Request:
{
  "email": "string"
}

Response:
{
  "success": true,
  "message": "Password reset email sent"
}
```

### POST /auth/reset-password
Reset password with token
```json
Request:
{
  "token": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "message": "Password reset successfully"
}
```

## Posts Endpoints

### GET /posts
Get paginated list of published posts
```json
Query Parameters:
- page: number (default: 1)
- limit: number (default: 10, max: 50)
- category: string (category slug)
- tag: string (tag slug)
- language: string (en|hi)
- search: string (search in title/content)
- sort: string (date|views|title)
- order: string (asc|desc)

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Inspirational Quotes",
      "slug": "inspirational-quotes",
      "excerpt": "Collection of motivational quotes...",
      "featured_image_url": "https://media.yourblog.com/...",
      "author": {
        "id": 1,
        "display_name": "John Doe",
        "avatar_url": "https://media.yourblog.com/..."
      },
      "categories": [
        {"id": 1, "name": "Quotes", "slug": "quotes"}
      ],
      "tags": [
        {"id": 1, "name": "Motivation", "slug": "motivation"}
      ],
      "language": "en",
      "view_count": 150,
      "comment_count": 5,
      "published_at": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_items": 95,
    "per_page": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

### GET /posts/{id}
Get single post by ID
```json
Response:
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Inspirational Quotes",
    "slug": "inspirational-quotes",
    "content": "Full post content here...",
    "excerpt": "Short excerpt...",
    "featured_image_url": "https://media.yourblog.com/...",
    "meta_title": "SEO title",
    "meta_description": "SEO description",
    "author": { /* author object */ },
    "categories": [ /* categories array */ ],
    "tags": [ /* tags array */ ],
    "language": "en",
    "view_count": 150,
    "comment_count": 5,
    "published_at": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### GET /posts/slug/{slug}
Get single post by slug
```json
Response: Same as GET /posts/{id}
```

### POST /posts
Create new post (requires authentication)
```json
Headers: Authorization: Bearer {token}

Request:
{
  "title": "string",
  "content": "string",
  "excerpt": "string",
  "status": "draft|published|private",
  "post_type": "post|page",
  "featured_image_url": "string",
  "meta_title": "string",
  "meta_description": "string",
  "language": "en|hi",
  "categories": [1, 2, 3],
  "tags": ["motivation", "inspiration"],
  "published_at": "2024-01-15T10:30:00Z"
}

Response:
{
  "success": true,
  "data": { /* full post object */ }
}
```

### PUT /posts/{id}
Update existing post (requires authentication)
```json
Headers: Authorization: Bearer {token}

Request: Same as POST /posts

Response:
{
  "success": true,
  "data": { /* updated post object */ }
}
```

### DELETE /posts/{id}
Delete post (requires authentication)
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "Post deleted successfully"
}
```

## Comments Endpoints

### GET /posts/{post_id}/comments
Get comments for a specific post
```json
Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- status: string (approved|pending|all) - admin only

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "post_id": 1,
      "parent_id": null,
      "author_name": "John Doe",
      "author_email": "<EMAIL>",
      "author_url": "https://johndoe.com",
      "content": "Great post! Very inspiring.",
      "status": "approved",
      "created_at": "2024-01-15T12:00:00Z",
      "replies": [
        {
          "id": 2,
          "parent_id": 1,
          "author_name": "Jane Smith",
          "content": "I agree!",
          "created_at": "2024-01-15T13:00:00Z"
        }
      ]
    }
  ],
  "pagination": { /* pagination object */ }
}
```

### POST /posts/{post_id}/comments
Add new comment
```json
Request:
{
  "author_name": "string",
  "author_email": "string",
  "author_url": "string",
  "content": "string",
  "parent_id": number // optional, for replies
}

Response:
{
  "success": true,
  "data": { /* comment object */ },
  "message": "Comment submitted for moderation"
}
```

### PUT /comments/{id}
Update comment (admin only)
```json
Headers: Authorization: Bearer {token}

Request:
{
  "content": "string",
  "status": "approved|pending|spam|trash"
}

Response:
{
  "success": true,
  "data": { /* updated comment object */ }
}
```

### DELETE /comments/{id}
Delete comment (admin only)
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "Comment deleted successfully"
}
```

## Categories Endpoints

### GET /categories
Get all categories
```json
Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Quotes",
      "slug": "quotes",
      "description": "Inspirational quotes",
      "parent_id": null,
      "post_count": 25,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### GET /categories/{id}
Get single category with posts
```json
Response:
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Quotes",
    "slug": "quotes",
    "description": "Inspirational quotes",
    "parent_id": null,
    "post_count": 25,
    "posts": [ /* paginated posts array */ ],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### POST /categories
Create new category (admin only)
```json
Headers: Authorization: Bearer {token}

Request:
{
  "name": "string",
  "slug": "string",
  "description": "string",
  "parent_id": number // optional
}

Response:
{
  "success": true,
  "data": { /* category object */ }
}
```

### PUT /categories/{id}
Update category (admin only)
```json
Headers: Authorization: Bearer {token}

Request: Same as POST /categories

Response:
{
  "success": true,
  "data": { /* updated category object */ }
}
```

### DELETE /categories/{id}
Delete category (admin only)
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "Category deleted successfully"
}
```

## Tags Endpoints

### GET /tags
Get all tags
```json
Query Parameters:
- search: string (search tag names)
- limit: number (default: 50)

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Motivation",
      "slug": "motivation",
      "post_count": 15,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### GET /tags/{id}
Get single tag with posts
```json
Response:
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Motivation",
    "slug": "motivation",
    "post_count": 15,
    "posts": [ /* paginated posts array */ ],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### POST /tags
Create new tag (authenticated users)
```json
Headers: Authorization: Bearer {token}

Request:
{
  "name": "string",
  "slug": "string",
  "description": "string"
}

Response:
{
  "success": true,
  "data": { /* tag object */ }
}
```

## Media Endpoints

### GET /media
Get paginated list of media files
```json
Headers: Authorization: Bearer {token}

Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- type: string (image|document|video)
- search: string (search filenames)

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "filename": "inspirational-quote.jpg",
      "original_filename": "my-quote.jpg",
      "file_path": "images/posts/2024/01/uuid-inspirational-quote.jpg",
      "file_size": 245760,
      "mime_type": "image/jpeg",
      "width": 1200,
      "height": 800,
      "alt_text": "Inspirational quote image",
      "caption": "Beautiful motivational quote",
      "url": "https://media.yourblog.com/images/posts/2024/01/uuid-inspirational-quote.jpg",
      "thumbnails": {
        "small": "https://media.yourblog.com/images/posts/2024/01/thumbnails/small-uuid-inspirational-quote.jpg",
        "medium": "https://media.yourblog.com/images/posts/2024/01/thumbnails/medium-uuid-inspirational-quote.jpg"
      },
      "uploaded_by": {
        "id": 1,
        "display_name": "John Doe"
      },
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "pagination": { /* pagination object */ }
}
```

### GET /media/{id}
Get single media file details
```json
Response:
{
  "success": true,
  "data": { /* full media object */ }
}
```

### POST /media/upload-url
Get presigned URL for direct upload to R2
```json
Headers: Authorization: Bearer {token}

Request:
{
  "filename": "string",
  "content_type": "string",
  "file_size": number
}

Response:
{
  "success": true,
  "upload_url": "https://blog-media.r2.cloudflarestorage.com/...",
  "file_key": "images/posts/2024/01/uuid-filename.jpg",
  "expires_in": 3600
}
```

### POST /media/confirm
Confirm upload and save metadata
```json
Headers: Authorization: Bearer {token}

Request:
{
  "file_key": "string",
  "alt_text": "string",
  "caption": "string"
}

Response:
{
  "success": true,
  "data": { /* media object */ }
}
```

### PUT /media/{id}
Update media metadata
```json
Headers: Authorization: Bearer {token}

Request:
{
  "alt_text": "string",
  "caption": "string"
}

Response:
{
  "success": true,
  "data": { /* updated media object */ }
}
```

### DELETE /media/{id}
Delete media file
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "message": "Media file deleted successfully"
}
```

## User Management Endpoints

### GET /users/profile
Get current user profile
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "display_name": "John Doe",
    "bio": "Content creator and blogger",
    "avatar_url": "https://media.yourblog.com/avatars/...",
    "role": "author",
    "status": "active",
    "email_verified": true,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### PUT /users/profile
Update current user profile
```json
Headers: Authorization: Bearer {token}

Request:
{
  "display_name": "string",
  "bio": "string",
  "avatar_url": "string"
}

Response:
{
  "success": true,
  "data": { /* updated user object */ }
}
```

### PUT /users/password
Change password
```json
Headers: Authorization: Bearer {token}

Request:
{
  "current_password": "string",
  "new_password": "string"
}

Response:
{
  "success": true,
  "message": "Password updated successfully"
}
```

### GET /users (Admin only)
Get paginated list of users
```json
Headers: Authorization: Bearer {token}

Query Parameters:
- page: number (default: 1)
- limit: number (default: 20)
- role: string (subscriber|author|editor|admin)
- status: string (active|inactive|banned)
- search: string (search usernames/emails)

Response:
{
  "success": true,
  "data": [ /* array of user objects */ ],
  "pagination": { /* pagination object */ }
}
```

### PUT /users/{id} (Admin only)
Update user role/status
```json
Headers: Authorization: Bearer {token}

Request:
{
  "role": "subscriber|author|editor|admin",
  "status": "active|inactive|banned"
}

Response:
{
  "success": true,
  "data": { /* updated user object */ }
}
```

## Analytics Endpoints

### GET /analytics/dashboard
Get dashboard analytics (admin only)
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "total_posts": 150,
    "total_users": 25,
    "total_comments": 300,
    "total_views": 15000,
    "recent_posts": [ /* recent posts array */ ],
    "popular_posts": [ /* popular posts array */ ],
    "recent_comments": [ /* recent comments array */ ],
    "monthly_stats": {
      "posts": 12,
      "users": 5,
      "comments": 45,
      "views": 2500
    }
  }
}
```

### GET /analytics/posts/{id}/views
Get post view analytics
```json
Headers: Authorization: Bearer {token}

Query Parameters:
- period: string (day|week|month|year)
- start_date: string (YYYY-MM-DD)
- end_date: string (YYYY-MM-DD)

Response:
{
  "success": true,
  "data": {
    "total_views": 1500,
    "unique_views": 1200,
    "daily_views": [
      {"date": "2024-01-15", "views": 150},
      {"date": "2024-01-16", "views": 200}
    ],
    "referrers": [
      {"source": "google.com", "views": 800},
      {"source": "direct", "views": 400}
    ]
  }
}
```

## Search Endpoints

### GET /search
Global search across posts
```json
Query Parameters:
- q: string (search query)
- type: string (posts|categories|tags|all)
- language: string (en|hi)
- limit: number (default: 20)

Response:
{
  "success": true,
  "data": {
    "posts": [ /* matching posts */ ],
    "categories": [ /* matching categories */ ],
    "tags": [ /* matching tags */ ],
    "total_results": 25
  }
}
```

## Settings Endpoints

### GET /settings (Admin only)
Get site settings
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "site_title": "ZayoTech Blog",
    "site_description": "Inspirational quotes and stories",
    "site_url": "https://yourblog.com",
    "posts_per_page": 10,
    "comment_moderation": true,
    "default_language": "en"
  }
}
```

### PUT /settings (Admin only)
Update site settings
```json
Headers: Authorization: Bearer {token}

Request:
{
  "site_title": "string",
  "site_description": "string",
  "posts_per_page": number,
  "comment_moderation": boolean,
  "default_language": "en|hi"
}

Response:
{
  "success": true,
  "data": { /* updated settings */ }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "message": "Email is required"
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Invalid input data
- `AUTHENTICATION_REQUIRED`: Missing or invalid token
- `AUTHORIZATION_FAILED`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `SERVER_ERROR`: Internal server error

## Rate Limiting

All endpoints are rate-limited:
- **Public endpoints**: 100 requests per minute per IP
- **Authenticated endpoints**: 1000 requests per minute per user
- **Upload endpoints**: 10 requests per minute per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```