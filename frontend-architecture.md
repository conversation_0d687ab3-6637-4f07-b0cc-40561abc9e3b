# Frontend Architecture Plan for Blog Website (Optimized for Maximum Speed)

## Technology Stack (Performance-Optimized)

### Core Framework Options (Choose One)

#### Option 1: Next.js with Static Export (Recommended)
- **Next.js 14+** with static export (`output: 'export'`)
- **React 18+** with Server Components for build-time rendering
- **TypeScript** for type safety
- **Zero runtime JavaScript** for content pages

#### Option 2: Astro (Ultra-Fast Alternative)
- **Astro 4+** with partial hydration
- **React components** only where needed
- **Minimal JavaScript** by default
- **Component-agnostic** (can mix React, Vue, Svelte)

#### Option 3: SvelteKit (Lightweight Option)
- **SvelteKit** with static adapter
- **Svelte 4+** for minimal bundle size
- **Fast compilation** and runtime performance

### Deployment Platform
- **Cloudflare Pages** - Perfect integration with Cloudflare ecosystem
- **Global CDN** with edge locations worldwide
- **Edge computing** with Cloudflare Workers
- **Free tier** with generous limits

### Performance Optimizations
- **Cloudflare Workers** for edge API routes and dynamic content
- **Cache API** for intelligent caching strategies
- **Brotli compression** and HTTP/3 enabled
- **WebP/AVIF images** with Cloudflare Image Optimization
- **Critical CSS inlining** for above-the-fold content
- **Resource hints** (preload, prefetch, preconnect)

### State Management (Minimal)
- **Native Web APIs** where possible
- **Lightweight state** with vanilla JS or Zustand (< 2KB)
- **Server-side rendering** for initial state
- **Progressive enhancement** approach

## Recommended Stack for Maximum Speed

### Next.js Static Export Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true, // Use Cloudflare Image Optimization instead
    domains: ['media.yourblog.com'],
  },
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Enable static optimization
  generateBuildId: async () => {
    return 'build-' + Date.now()
  }
}

module.exports = nextConfig
```

### Cloudflare Pages Configuration
```toml
# wrangler.toml for Pages
name = "blog-frontend"
compatibility_date = "2024-01-01"

[env.production]
vars = { NODE_ENV = "production" }

[[env.production.routes]]
pattern = "/api/*"
custom_domain = "yourblog.com"

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.upload]
format = "modules"
dir = "out"
main = "./index.js"
```

## Project Structure (Optimized)

```
blog-frontend/
├── src/
│   ├── app/                      # Next.js App Router (Static Export)
│   │   ├── (static)/             # Static pages group
│   │   │   ├── page.tsx          # Home page (SSG)
│   │   │   ├── posts/
│   │   │   │   ├── [slug]/       # Individual posts (SSG)
│   │   │   │   └── page/[page]/  # Paginated posts (SSG)
│   │   │   ├── category/
│   │   │   │   └── [slug]/       # Category pages (SSG)
│   │   │   └── tag/
│   │   │       └── [slug]/       # Tag pages (SSG)
│   │   ├── (dynamic)/            # Dynamic features (Client-side)
│   │   │   ├── search/           # Search interface
│   │   │   └── admin/            # Admin dashboard
│   │   ├── globals.css           # Critical CSS only
│   │   └── layout.tsx            # Minimal layout
│   ├── components/               # Lightweight components
│   │   ├── ui/                   # Minimal UI components
│   │   ├── blog/                 # Blog-specific components
│   │   └── layout/               # Layout components
│   ├── lib/                      # Utility functions
│   │   ├── api.ts                # Edge API client
│   │   ├── cache.ts              # Cloudflare Cache API
│   │   ├── images.ts             # Image optimization
│   │   └── seo.ts                # SEO utilities
│   ├── styles/                   # CSS organization
│   │   ├── critical.css          # Above-the-fold CSS
│   │   ├── components.css        # Component styles
│   │   └── utilities.css         # Utility classes
│   └── types/                    # TypeScript definitions
├── workers/                      # Cloudflare Workers
│   ├── api/                      # API endpoints
│   ├── cache/                    # Cache management
│   └── images/                   # Image processing
├── public/                       # Static assets (optimized)
│   ├── images/                   # Optimized images
│   ├── icons/                    # SVG icons
│   └── fonts/                    # Web fonts (subset)
└── scripts/                      # Build scripts
    ├── generate-static.js        # Static generation
    ├── optimize-images.js        # Image optimization
    └── critical-css.js           # Critical CSS extraction
```

## Performance-First Component Architecture

### 1. Static Layout Components (Zero JS)

#### Minimal Layout (Static)
```typescript
// components/layout/StaticLayout.tsx
interface StaticLayoutProps {
  children: React.ReactNode;
  title: string;
  description: string;
}

export function StaticLayout({ children, title, description }: StaticLayoutProps) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>{title}</title>
        <meta name="description" content={description} />

        {/* Critical CSS inlined */}
        <style dangerouslySetInnerHTML={{ __html: criticalCSS }} />

        {/* Preload key resources */}
        <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="" />
        <link rel="preconnect" href="https://media.yourblog.com" />

        {/* Non-critical CSS loaded asynchronously */}
        <link rel="preload" href="/styles/main.css" as="style" onLoad="this.onload=null;this.rel='stylesheet'" />
        <noscript><link rel="stylesheet" href="/styles/main.css" /></noscript>
      </head>
      <body className="font-sans antialiased">
        <StaticHeader />
        <main className="min-h-screen">
          {children}
        </main>
        <StaticFooter />

        {/* Load non-critical JS only when needed */}
        <script type="module" src="/js/progressive-enhancement.js" async />
      </body>
    </html>
  );
}
```

#### Static Header (No JavaScript)
```typescript
// components/layout/StaticHeader.tsx
export function StaticHeader() {
  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-6">
            <a href="/" className="text-xl font-bold">
              ZayoTech Blog
            </a>
            <nav className="hidden md:flex space-x-6">
              <a href="/category/quotes" className="hover:text-blue-600">Quotes</a>
              <a href="/category/shayari" className="hover:text-blue-600">Shayari</a>
              <a href="/category/stories" className="hover:text-blue-600">Stories</a>
              <a href="/category/hindi" className="hover:text-blue-600">हिंदी</a>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            {/* Progressive enhancement for search */}
            <button
              className="search-toggle p-2 rounded-md hover:bg-gray-100"
              data-search-toggle
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
```

### 2. Static Blog Components (Performance Optimized)

#### Static Post Card (No JavaScript)
```typescript
// components/blog/StaticPostCard.tsx
interface StaticPostCardProps {
  post: Post;
  variant?: 'default' | 'featured' | 'compact';
}

export function StaticPostCard({ post, variant = 'default' }: StaticPostCardProps) {
  const cardClasses = [
    "group relative overflow-hidden rounded-lg border bg-white shadow-sm hover:shadow-md transition-shadow",
    variant === 'featured' && "md:col-span-2",
    variant === 'compact' && "flex space-x-4"
  ].filter(Boolean).join(' ');

  return (
    <article className={cardClasses}>
      {post.featured_image_url && (
        <div className={variant === 'compact' ? "aspect-square w-24 flex-shrink-0 overflow-hidden" : "aspect-video overflow-hidden"}>
          {/* Use Cloudflare Image Optimization */}
          <img
            src={`https://media.yourblog.com/cdn-cgi/image/width=600,height=400,format=webp,quality=85/${post.featured_image_url}`}
            alt={post.title}
            className="object-cover w-full h-full transition-transform group-hover:scale-105"
            loading="lazy"
            decoding="async"
            width="600"
            height="400"
          />
        </div>
      )}
      <div className="p-6">
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
          <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
            {post.categories[0]?.name}
          </span>
          <span>•</span>
          <time dateTime={post.published_at}>
            {new Date(post.published_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}
          </time>
        </div>
        <h3 className="font-semibold leading-tight mb-2 text-lg">
          <a href={`/posts/${post.slug}/`} className="hover:text-blue-600 transition-colors">
            {post.title}
          </a>
        </h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {post.excerpt}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium">
              {post.author.display_name[0]}
            </div>
            <span className="text-sm text-gray-600">
              {post.author.display_name}
            </span>
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <span>{post.view_count}</span>
            </span>
            <span className="flex items-center space-x-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span>{post.comment_count}</span>
            </span>
          </div>
        </div>
      </div>
    </article>
  );
}
```

#### PostContent
```typescript
// components/blog/PostContent.tsx
interface PostContentProps {
  post: Post;
}

export function PostContent({ post }: PostContentProps) {
  return (
    <article className="prose prose-lg dark:prose-invert max-w-none">
      <header className="mb-8">
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
          {post.categories.map((category) => (
            <Badge key={category.id} variant="secondary">
              {category.name}
            </Badge>
          ))}
          <span>•</span>
          <time>{formatDate(post.published_at)}</time>
          <span>•</span>
          <span>{post.view_count} views</span>
        </div>
        <h1 className="text-4xl font-bold leading-tight mb-4">{post.title}</h1>
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={post.author.avatar_url} />
            <AvatarFallback>{post.author.display_name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{post.author.display_name}</p>
            <p className="text-sm text-muted-foreground">
              {formatDate(post.published_at)}
            </p>
          </div>
        </div>
      </header>

      {post.featured_image_url && (
        <div className="aspect-video overflow-hidden rounded-lg mb-8">
          <Image
            src={post.featured_image_url}
            alt={post.title}
            className="object-cover"
            fill
          />
        </div>
      )}

      <div className="content">
        <ReactMarkdown
          components={{
            img: ({ src, alt }) => (
              <Image
                src={src || ''}
                alt={alt || ''}
                className="rounded-lg"
                width={800}
                height={400}
              />
            ),
          }}
        >
          {post.content}
        </ReactMarkdown>
      </div>

      <footer className="mt-8 pt-8 border-t">
        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.map((tag) => (
            <Badge key={tag.id} variant="outline">
              #{tag.name}
            </Badge>
          ))}
        </div>
        <ShareButtons post={post} />
      </footer>
    </article>
  );
}
```

### 3. Admin Components

#### AdminLayout
```typescript
// components/admin/AdminLayout.tsx
export function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
```

#### PostEditor
```typescript
// components/admin/PostEditor.tsx
interface PostEditorProps {
  post?: Post;
  onSave: (data: PostFormData) => void;
}

export function PostEditor({ post, onSave }: PostEditorProps) {
  const form = useForm<PostFormData>({
    defaultValues: post || {
      title: '',
      content: '',
      excerpt: '',
      status: 'draft',
      categories: [],
      tags: [],
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSave)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter post title" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Content</FormLabel>
                  <FormControl>
                    <RichTextEditor {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="excerpt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Excerpt</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Brief description" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Publish</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="private">Private</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex space-x-2">
                  <Button type="submit" variant="default">
                    Save
                  </Button>
                  <Button type="button" variant="outline">
                    Preview
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <CategorySelector
                  value={form.watch('categories')}
                  onChange={(categories) => form.setValue('categories', categories)}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <TagSelector
                  value={form.watch('tags')}
                  onChange={(tags) => form.setValue('tags', tags)}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <MediaUploader
                  value={form.watch('featured_image_url')}
                  onChange={(url) => form.setValue('featured_image_url', url)}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </Form>
  );
}
```

## Cloudflare Workers Integration

### 1. Edge API Routes
```typescript
// workers/api/posts.ts
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    const cache = caches.default;

    // Check cache first
    const cacheKey = new Request(url.toString(), request);
    const cachedResponse = await cache.match(cacheKey);

    if (cachedResponse) {
      return cachedResponse;
    }

    // Fetch from D1
    const posts = await env.DB.prepare(
      "SELECT * FROM posts WHERE status = 'published' ORDER BY published_at DESC LIMIT 20"
    ).all();

    const response = new Response(JSON.stringify({
      success: true,
      data: posts.results
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300, s-maxage=3600',
        'CDN-Cache-Control': 'max-age=3600',
        'Cloudflare-CDN-Cache-Control': 'max-age=86400'
      }
    });

    // Cache for 1 hour
    await cache.put(cacheKey, response.clone());
    return response;
  }
};
```

### 2. Image Optimization Worker
```typescript
// workers/images/optimize.ts
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    const imageUrl = url.searchParams.get('url');
    const width = url.searchParams.get('w') || '800';
    const quality = url.searchParams.get('q') || '85';
    const format = url.searchParams.get('f') || 'webp';

    if (!imageUrl) {
      return new Response('Missing image URL', { status: 400 });
    }

    // Use Cloudflare Image Resizing
    const optimizedUrl = `https://media.yourblog.com/cdn-cgi/image/width=${width},quality=${quality},format=${format}/${imageUrl}`;

    return fetch(optimizedUrl, {
      cf: {
        cacheEverything: true,
        cacheTtl: 86400 * 30, // 30 days
      }
    });
  }
};
```

### 3. Cache Management Worker
```typescript
// workers/cache/manager.ts
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();

    switch (action) {
      case 'purge':
        return await this.purgeCache(request, env);
      case 'warm':
        return await this.warmCache(request, env);
      default:
        return new Response('Invalid action', { status: 400 });
    }
  },

  async purgeCache(request: Request, env: Env): Promise<Response> {
    const { tags } = await request.json();

    // Purge by cache tags
    await fetch('https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.CF_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ tags })
    });

    return new Response('Cache purged', { status: 200 });
  },

  async warmCache(request: Request, env: Env): Promise<Response> {
    const { urls } = await request.json();

    // Pre-warm cache for critical pages
    const promises = urls.map((url: string) =>
      fetch(url, { cf: { cacheEverything: true } })
    );

    await Promise.all(promises);
    return new Response('Cache warmed', { status: 200 });
  }
};
```

## Performance Optimization Strategies

### 1. Critical CSS Extraction
```javascript
// scripts/critical-css.js
const critical = require('critical');
const fs = require('fs');
const path = require('path');

async function extractCriticalCSS() {
  const pages = [
    { url: 'http://localhost:3000/', output: 'home' },
    { url: 'http://localhost:3000/posts/sample-post/', output: 'post' },
    { url: 'http://localhost:3000/category/quotes/', output: 'category' }
  ];

  for (const page of pages) {
    const { css } = await critical.generate({
      base: 'out/',
      src: page.url,
      width: 1300,
      height: 900,
      inline: false,
      extract: true,
      ignore: {
        atrule: ['@font-face'],
        rule: [/\.hidden/],
        decl: (node, value) => /url\(/.test(value)
      }
    });

    fs.writeFileSync(
      path.join('src/styles/critical', `${page.output}.css`),
      css
    );
  }
}

extractCriticalCSS();
```

### 2. Resource Optimization
```typescript
// lib/performance.ts
export class PerformanceOptimizer {
  static preloadCriticalResources() {
    // Preload critical fonts
    const fontLink = document.createElement('link');
    fontLink.rel = 'preload';
    fontLink.href = '/fonts/inter-var.woff2';
    fontLink.as = 'font';
    fontLink.type = 'font/woff2';
    fontLink.crossOrigin = '';
    document.head.appendChild(fontLink);

    // Preconnect to external domains
    const preconnectDomains = [
      'https://media.yourblog.com',
      'https://api.yourblog.com'
    ];

    preconnectDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      document.head.appendChild(link);
    });
  }

  static lazyLoadImages() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src!;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }

  static prefetchNextPage() {
    // Prefetch next page on hover
    document.querySelectorAll('a[href^="/posts/"]').forEach(link => {
      link.addEventListener('mouseenter', () => {
        const prefetchLink = document.createElement('link');
        prefetchLink.rel = 'prefetch';
        prefetchLink.href = (link as HTMLAnchorElement).href;
        document.head.appendChild(prefetchLink);
      }, { once: true });
    });
  }
}
```

## Routing Strategy

### 1. App Router Structure
```typescript
// app/layout.tsx
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            {children}
          </div>
        </Providers>
      </body>
    </html>
  );
}

// app/(blog)/layout.tsx
export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <MainLayout>{children}</MainLayout>;
}

// app/(admin)/layout.tsx
export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <AdminLayout>{children}</AdminLayout>;
}
```

### 2. Dynamic Routes
```typescript
// app/(blog)/posts/[slug]/page.tsx
export default async function PostPage({
  params,
}: {
  params: { slug: string };
}) {
  const post = await getPost(params.slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="max-w-4xl mx-auto">
      <PostContent post={post} />
      <CommentSection postId={post.id} />
    </div>
  );
}

// app/(blog)/category/[slug]/page.tsx
export default async function CategoryPage({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { page?: string };
}) {
  const page = Number(searchParams.page) || 1;
  const { posts, category } = await getPostsByCategory(params.slug, page);

  return (
    <div>
      <CategoryHeader category={category} />
      <PostGrid posts={posts} />
      <Pagination />
    </div>
  );
}
```

## Internationalization

### 1. Configuration
```typescript
// next.config.js
const withNextIntl = require('next-intl/plugin')('./i18n.ts');

module.exports = withNextIntl({
  // Other Next.js config
});

// i18n.ts
import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async ({ locale }) => ({
  messages: (await import(`./messages/${locale}.json`)).default,
}));
```

### 2. Usage
```typescript
// components/LanguageToggle.tsx
import { useTranslations } from 'next-intl';

export function LanguageToggle() {
  const t = useTranslations('common');
  const { language, setLanguage } = useUIStore();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <Globe className="h-4 w-4" />
          <span className="ml-2">{language.toUpperCase()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem onClick={() => setLanguage('en')}>
          English
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setLanguage('hi')}>
          हिंदी
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

## Performance Optimization

### 1. Image Optimization
```typescript
// components/OptimizedImage.tsx
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
}
```

### 2. Code Splitting
```typescript
// Dynamic imports for heavy components
const RichTextEditor = dynamic(() => import('./RichTextEditor'), {
  loading: () => <div>Loading editor...</div>,
  ssr: false,
});

const AdminDashboard = dynamic(() => import('./AdminDashboard'), {
  loading: () => <DashboardSkeleton />,
});
```

### 3. Caching Strategy
```typescript
// lib/cache.ts
export const revalidate = 3600; // 1 hour

// For static generation
export async function generateStaticParams() {
  const posts = await getPosts({ limit: 100 });
  return posts.map((post) => ({ slug: post.slug }));
}

// For incremental static regeneration
export const dynamic = 'force-static';
export const revalidate = 3600;
```

## SEO Optimization

### 1. Metadata Generation
```typescript
// app/(blog)/posts/[slug]/page.tsx
export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  const post = await getPost(params.slug);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.meta_title || post.title,
    description: post.meta_description || post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: post.featured_image_url ? [post.featured_image_url] : [],
      type: 'article',
      publishedTime: post.published_at,
      authors: [post.author.display_name],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      images: post.featured_image_url ? [post.featured_image_url] : [],
    },
  };
}
```

### 2. Structured Data
```typescript
// components/StructuredData.tsx
export function PostStructuredData({ post }: { post: Post }) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.featured_image_url,
    author: {
      '@type': 'Person',
      name: post.author.display_name,
    },
    publisher: {
      '@type': 'Organization',
      name: 'ZayoTech Blog',
    },
    datePublished: post.published_at,
    dateModified: post.updated_at,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
```

## Build Optimization

### 1. Static Generation Script
```javascript
// scripts/generate-static.js
const fs = require('fs');
const path = require('path');

async function generateStaticSite() {
  console.log('🚀 Starting static site generation...');

  // Fetch all posts for static generation
  const posts = await fetch('https://api.yourblog.com/v1/posts?limit=1000')
    .then(res => res.json());

  // Generate static paths
  const staticPaths = {
    '/': { priority: 1.0, changefreq: 'daily' },
    '/about/': { priority: 0.8, changefreq: 'monthly' },
    '/contact/': { priority: 0.6, changefreq: 'monthly' }
  };

  // Add post paths
  posts.data.forEach(post => {
    staticPaths[`/posts/${post.slug}/`] = {
      priority: 0.9,
      changefreq: 'weekly',
      lastmod: post.updated_at
    };
  });

  // Generate sitemap
  const sitemap = generateSitemap(staticPaths);
  fs.writeFileSync('public/sitemap.xml', sitemap);

  console.log(`✅ Generated ${Object.keys(staticPaths).length} static pages`);
}

function generateSitemap(paths) {
  const urls = Object.entries(paths).map(([path, meta]) => `
    <url>
      <loc>https://yourblog.com${path}</loc>
      <lastmod>${meta.lastmod || new Date().toISOString()}</lastmod>
      <changefreq>${meta.changefreq}</changefreq>
      <priority>${meta.priority}</priority>
    </url>
  `).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${urls}
    </urlset>`;
}

generateStaticSite();
```

### 2. Image Optimization Script
```javascript
// scripts/optimize-images.js
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function optimizeImages() {
  const inputDir = 'public/images';
  const outputDir = 'public/optimized';

  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const images = fs.readdirSync(inputDir)
    .filter(file => /\.(jpg|jpeg|png)$/i.test(file));

  for (const image of images) {
    const inputPath = path.join(inputDir, image);
    const name = path.parse(image).name;

    // Generate WebP versions
    await sharp(inputPath)
      .webp({ quality: 85 })
      .toFile(path.join(outputDir, `${name}.webp`));

    // Generate AVIF versions
    await sharp(inputPath)
      .avif({ quality: 80 })
      .toFile(path.join(outputDir, `${name}.avif`));

    // Generate responsive sizes
    const sizes = [400, 800, 1200];
    for (const size of sizes) {
      await sharp(inputPath)
        .resize(size, null, { withoutEnlargement: true })
        .webp({ quality: 85 })
        .toFile(path.join(outputDir, `${name}-${size}w.webp`));
    }
  }

  console.log(`✅ Optimized ${images.length} images`);
}

optimizeImages();
```

## Cloudflare Pages Deployment

### 1. Pages Configuration
```toml
# wrangler.toml
name = "blog-frontend"
compatibility_date = "2024-01-01"

[env.production]
vars = { NODE_ENV = "production" }

[env.production.routes]
pattern = "yourblog.com/*"
zone_name = "yourblog.com"

[build]
command = "npm run build:static"
cwd = "."
watch_dir = "src"

[build.upload]
format = "service-worker"
dir = "out"
main = "./index.js"
```

### 2. Build Commands
```json
// package.json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "build:static": "npm run generate:static && next build && next export",
    "generate:static": "node scripts/generate-static.js",
    "optimize:images": "node scripts/optimize-images.js",
    "critical:css": "node scripts/critical-css.js",
    "deploy": "wrangler pages publish out --project-name=blog-frontend"
  }
}
```

### 3. Performance Headers
```javascript
// public/_headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

/*.html
  Cache-Control: public, max-age=0, must-revalidate

/*.css
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.woff2
  Cache-Control: public, max-age=31536000, immutable

/images/*
  Cache-Control: public, max-age=31536000, immutable

/api/*
  Cache-Control: public, max-age=300, s-maxage=3600
```

## Performance Metrics & Monitoring

### 1. Core Web Vitals Optimization
```typescript
// lib/web-vitals.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric: any) {
  // Send to Cloudflare Analytics or your preferred service
  fetch('/api/analytics', {
    method: 'POST',
    body: JSON.stringify(metric),
    headers: { 'Content-Type': 'application/json' }
  });
}

// Measure all Core Web Vitals
getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

### 2. Performance Budget
```javascript
// performance-budget.js
module.exports = {
  budget: [
    {
      path: '/*',
      timings: [
        { metric: 'first-contentful-paint', budget: 2000 },
        { metric: 'largest-contentful-paint', budget: 2500 },
        { metric: 'cumulative-layout-shift', budget: 0.1 },
        { metric: 'total-blocking-time', budget: 300 }
      ],
      resourceSizes: [
        { resourceType: 'script', budget: 150 },
        { resourceType: 'total', budget: 500 },
        { resourceType: 'stylesheet', budget: 50 },
        { resourceType: 'image', budget: 200 }
      ]
    }
  ]
};
```

## Expected Performance Results

### Lighthouse Scores (Target)
- **Performance**: 95-100
- **Accessibility**: 95-100
- **Best Practices**: 95-100
- **SEO**: 95-100

### Core Web Vitals (Target)
- **LCP**: < 1.2s (Good)
- **FID**: < 100ms (Good)
- **CLS**: < 0.1 (Good)
- **TTFB**: < 200ms (Good)

### Bundle Size (Target)
- **Initial JS**: < 50KB gzipped
- **CSS**: < 20KB gzipped
- **Images**: WebP/AVIF optimized
- **Total Page Weight**: < 500KB

This performance-optimized frontend architecture leverages Cloudflare's edge computing platform to deliver the fastest possible website experience while maintaining excellent SEO and user experience.
```